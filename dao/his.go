// Package dao 患者信息处理包
// 负责患者数据的获取、处理、Redis缓存和数据库同步
package dao

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"

	"github.com/sirupsen/logrus"                        // 日志库
	"gitlab.itingluo.com/backend/ivankabasepush/cache"  // Redis缓存
	"gitlab.itingluo.com/backend/ivankabasepush/models" // 数据模型
	std "gitlab.itingluo.com/backend/ivankastd"         // 标准库
	"golang.org/x/text/encoding/simplifiedchinese"      // 中文编码转换
)

// PostPatientLastTimeReq 获取患者最后时间请求结构体
// 用于请求获取患者数据的最后更新时间
type PostPatientLastTimeReq struct {
	TlClientId int `json:"tl_client_id"` // 听力客户端ID
}

// Patients 患者信息结构体
// 包含患者的完整信息，用于数据库查询和Redis存储
type Patients struct {
	Name              string `json:"name"`                              // 患者姓名
	IDCard            string `json:"id_card"`                           // 身份证号
	Mobile            string `json:"mobile"`                            // 手机号码
	Sex               string `json:"sex"`                               // 性别
	Age               int    `json:"age"`                               // 年龄
	HospitalizationNo string `json:"hospitalization_no"`                // 住院号
	SickbedNo         string `json:"sickbed_no"`                        // 床位号
	InhospitalTime    int64  `json:"inhospital_time"`                   // 入院时间（Unix时间戳）
	OuthospitalTime   int64  `json:"outhospital_time"`                  // 出院时间（Unix时间戳）
	Status            int    `json:"status"`                            // 患者状态（1:在院，2:出院）
	Category          string `json:"category" gorm:"column:catetegory"` // 患者分类
	InpatientWard     string `json:"inpatient_ward"`                    // 病区
	DoctorID          string `json:"doctor_id"`                         // 医生ID
	InpatientInfoId   string `json:"inpatient_info_id"`                 // 住院信息ID
	NurseId           string `json:"nurse_id"`                          // 护士ID
	Birthday          string `json:"birthday"`                          // 生日
}

// InPatientToRedis 从数据库获取患者信息并写入Redis
// 根据患者状态从不同数据库获取患者数据，然后保存到Redis缓存
// 参数：
//   - tlClientId: 听力客户端ID
//   - Status: 患者状态（In:入院, Out:出院, Up:在院）
//   - DbName: 数据库类型
func InPatientToRedis(tlClientId string, Status string, DbName string) {
	log.Println(Status + " begin " + DbName)
	resIn := make([]Patients, 0) // 初始化患者数据切片

	// 从数据库获取患者数据
	err := GetPatient(&resIn, Status, tlClientId, DbName)
	if err != nil {
		// 如果Redis键不存在，直接返回
		if err.Error() == "redis: nil" {
			return
		}
		// 记录错误日志
		std.LogErrorLnAndFields(logrus.Fields{
			"topic":      "writeRedis",
			"tlClientId": tlClientId,
		}, "first", "writeRedis", err)
	}

	// 将患者数据保存到Redis
	SavePatientByRedis(&resIn, Status, tlClientId)
}

// GetPatient 从数据库获取患者信息
// 根据患者状态和最后更新时间从不同类型的数据库中查询患者数据
// 参数：
//   - res: 存储查询结果的患者切片指针
//   - status: 患者状态（In:入院, Out:出院, Up:在院）
//   - tlClientId: 听力客户端ID字符串
//   - DbName: 数据库类型（sqlserver/oracle/sybase/mysql）
func GetPatient(res *[]Patients, status string, tlClientId string, DbName string) error {
	// 转换客户端ID为整数
	tid, err := strconv.Atoi(tlClientId)
	log.Println("tl_client_id:", tid)
	if err != nil {
		return err
	}

	var lastTime int
	// 对于入院和出院状态，需要获取最后更新时间进行增量同步
	if status != "Up" {
		lastTime, err = GetLastTimeByRedis(tid, status)
		if err != nil {
			log.Println(err)
			return err
		}
		// 如果最后时间为0，表示没有新数据需要同步
		if lastTime == 0 {
			log.Println("lastTime==0")
			return nil
		}
	}

	var totalRecords int64 // 记录总数
	var w string           // WHERE条件字符串
	var query string       // SQL查询语句

	// 根据患者状态构建不同的查询条件
	switch status {
	case "In": // 入院患者查询条件
		if DbName == "oracle" {
			// Oracle时间戳转换：从Unix时间戳转为Oracle日期格式
			w = fmt.Sprintf("inhospital_time > TO_DATE('1970-01-01 08:00:00', 'YYYY-MM-DD HH24:MI:SS') + NUMTODSINTERVAL(%d, 'SECOND')", lastTime+1)
		} else if DbName == "mysql" {
			// MySQL时间戳转换
			w = fmt.Sprintf("inhospital_time > DATE_ADD('1970-01-01 08:00:00', INTERVAL %d SECOND)", lastTime+1)
		} else {
			// SQL Server时间戳转换
			w = fmt.Sprintf("inhospital_time > DATEADD(SECOND, %d, '1970-01-01 08:00:00')", lastTime+1)
		}

	case "Out": // 出院患者查询条件
		// 注释：处理Redis返回的时间戳，判断是否为当天整点时间戳
		// 如果是整点时间戳，可能需要获取更长时间范围的数据（如5个月前）
		// 以下为相关逻辑的注释代码，目前使用简化版本
		//lastTime := int64(lastTime)
		//lastTimeAsTime := time.Unix(lastTime, 0)
		//today := time.Date(lastTimeAsTime.Year(), lastTimeAsTime.Month(), lastTimeAsTime.Day(), 0, 0, 0, 0, lastTimeAsTime.Location())
		//todayTimestamp := today.Unix()
		//log.Println("出院时间戳:", todayTimestamp)
		//log.Println("lastTime", lastTime)

		if DbName == "oracle" {
			// Oracle出院时间查询条件
			w = fmt.Sprintf("outhospital_time > TO_DATE('1970-01-01 08:00:00', 'YYYY-MM-DD HH24:MI:SS') + NUMTODSINTERVAL(%d, 'SECOND')", lastTime)
		} else if DbName == "mysql" {
			// MySQL出院时间查询条件，同时过滤状态为2（出院）的患者
			w = fmt.Sprintf("outhospital_time > DATE_ADD('1970-01-01 08:00:00', INTERVAL %d SECOND) and status=2", lastTime)
		} else {
			// SQL Server出院时间查询条件
			w = fmt.Sprintf("outhospital_time > DATEADD(SECOND, %d, '1970-01-01 08:00:00') and status=2", lastTime)
		}

	case "Up": // 在院患者查询条件
		// 根据不同的客户端ID使用不同的在院判断逻辑
		switch tid {
		case 1000000000332:
			// 特定客户端：出院时间为1900年表示在院
			w = " outhospital_time='1900-01-01 00:00:00.000'"
		case 1000000000292:
			// 特定客户端：出院时间为空表示在院
			w = " outhospital_time is null"
		case 1000000000302, 1000000000356:
			// 特定客户端：出院时间为空或状态为1表示在院
			w = " outhospital_time is null or status = 1"
		default:
			// 默认：状态为1表示在院
			w = "status = 1"
		}
	}
	switch DbName {
	//处理sqlserver
	case "sqlserver":
		db := models.DB()
		switch tid {
		case 1000000000385:
			if err := db.Table("His.view_spt_patient as vsp").Select("name,id_card,mobile,sex,age," +
				"hospitalization_no,sickbed_no," +
				"CAST(DATEDIFF(SECOND, '1970-01-01 08:00:00', inhospital_time) AS bigint) AS inhospital_time," +
				"CASE WHEN outhospital_time = '1900-01-01 00:00:00.000' THEN 0 ELSE DATEDIFF(SECOND, '1970-01-01 08:00:00', outhospital_time) END AS outhospital_time," +
				"status,catetegory,(SELECT name FROM His.view_spt_department WHERE department_id = vsp.dept_id) AS inpatient_ward,doctor_id,inpatient_info_id,nurse_id,birthday").Where(w).Count(&totalRecords).Find(&res).Error; err != nil {
				return err
			}
		case 1000000000365:
			if err := db.Table("view_spt_patient").Select("name,id_card,mobile,sex,age," +
				"hospitalization_no,sickbed_no," +
				"CAST(DATEDIFF(SECOND, '1970-01-01 08:00:00', inhospital_time) AS bigint) AS inhospital_time," +
				"(CASE WHEN outhospital_time = '1970-01-01 00:00:00.000' THEN 0 else CAST(DATEDIFF(SECOND, '1970-01-01 08:00:00', outhospital_time) AS bigint) end )AS outhospital_time," +
				"status,catetegory,inpatient_ward,doctor_id,inpatient_info_id,nurse_id,birthday").Where(w).Count(&totalRecords).Find(&res).Error; err != nil {
				return err
			}
		case 1000000000332:
			if err := db.Table("view_spt_patient").Select("name,id_card,mobile,sex,age," +
				"hospitalization_no,sickbed_no," +
				"CAST(DATEDIFF(SECOND, '1970-01-01 08:00:00', inhospital_time) AS bigint) AS inhospital_time," +
				"(CASE WHEN outhospital_time < '1970-01-01' THEN 0 else CAST(DATEDIFF(SECOND, '1970-01-01 08:00:00', outhospital_time) AS bigint) end )AS outhospital_time," +
				"CASE WHEN outhospital_time = '1900-01-01 00:00:00.000' THEN 1 else 2 END as status,catetegory,inpatient_ward,doctor_id,inpatient_info_id,nurse_id,birthday").Where(w).Count(&totalRecords).Find(&res).Error; err != nil {
				return err
			}
		default:
			if err := db.Table("view_spt_patient").Select("name,id_card,mobile,sex,age," +
				"hospitalization_no,sickbed_no," +
				"CAST(DATEDIFF(SECOND, '1970-01-01 08:00:00', inhospital_time) AS bigint) AS inhospital_time," +
				"CAST(DATEDIFF(SECOND, '1970-01-01 08:00:00', outhospital_time) AS bigint) AS outhospital_time," +
				"status,catetegory,inpatient_ward,doctor_id,inpatient_info_id,nurse_id,birthday").Where(w).Count(&totalRecords).Find(&res).Error; err != nil {
				return err
			}
		}

		for i, _ := range *res {
			(*res)[i].Name = strings.TrimSpace((*res)[i].Name)
			(*res)[i].IDCard = strings.TrimSpace((*res)[i].IDCard)
			(*res)[i].Mobile = strings.TrimSpace((*res)[i].Mobile)
			(*res)[i].Sex = strings.TrimSpace((*res)[i].Sex)
			if tid == 1000000000365 {
				(*res)[i].HospitalizationNo = strings.TrimSpace((*res)[i].InpatientInfoId)
			} else {
				(*res)[i].HospitalizationNo = strings.TrimSpace((*res)[i].HospitalizationNo)
			}
			(*res)[i].SickbedNo = strings.TrimSpace((*res)[i].SickbedNo)
			(*res)[i].Category = strings.TrimSpace((*res)[i].Category)
			(*res)[i].InpatientWard = strings.TrimSpace((*res)[i].InpatientWard)
			switch tid {
			//大余瑞康医院
			case 1000000000370:
				(*res)[i].DoctorID = "3"
			//南昌康宁医院
			case 1000000000359:
				(*res)[i].DoctorID = "10176"
			default:
				(*res)[i].DoctorID = strings.TrimSpace((*res)[i].DoctorID)
			}
			(*res)[i].Status = (*res)[i].Status

			(*res)[i].InpatientInfoId = strings.TrimSpace((*res)[i].InpatientInfoId)
			(*res)[i].NurseId = strings.TrimSpace((*res)[i].NurseId)
			(*res)[i].Birthday = strings.TrimSpace((*res)[i].Birthday)
		}
		return nil
		//处理oracle
	case "oracle":
		db := models.OraclDB()
		switch tid {
		//	万年惠康 //
		case 1000000000292:
			query = "SELECT " +
				"name, " +
				"COALESCE(id_card, '') AS id_card, " +
				"COALESCE(mobile, '') AS mobile, " +
				"sex, " +
				"age, " +
				"hospitalization_no, " +
				"sickbed_no, " +
				"CAST((inhospital_time - TO_DATE('1970-01-01 08:00:00', 'YYYY-MM-DD HH24:MI:SS')) * 24 * 60 * 60 AS NUMBER(18,0)) AS inhospital_time, " +
				"CASE " +
				"WHEN outhospital_time is null THEN 0 " +
				"ELSE CAST((outhospital_time - TO_DATE('1970-01-01 08:00:00', 'YYYY-MM-DD HH24:MI:SS')) * 24 * 60 * 60 AS NUMBER(18,0)) " +
				"END AS outhospital_time, " +
				"case when outhospital_time is null then 1 else 2 end as status, " +
				"catetegory, " +
				"inpatient_ward, " +
				"doctor_id, " +
				"CONCAT(hospitalization_no, TO_CHAR(inhospital_time, 'YYYY-MM-DD')) as inpatient_info_id ,nurse_id ,birthday " +
				"FROM " +
				"view_spt_patient " +
				"WHERE " + w
			//高安
		case 1000000000252:
			query = "SELECT " +
				"name, coalesce(id_card,''), mobile,sex, EXTRACT(YEAR FROM SYSDATE) - EXTRACT(YEAR FROM birthday) AS age, hospitalization_no, sickbed_no, " +
				"(CAST((inhospital_time - TO_DATE('1970-01-01 08:00:00', 'YYYY-MM-DD HH24:MI:SS')) * 24 * 60 * 60 AS NUMBER(18,0))) AS inhospital_time, " +
				"coalesce((CAST((outhospital_time - TO_DATE('1970-01-01 08:00:00', 'YYYY-MM-DD HH24:MI:SS')) * 24 * 60 * 60 AS NUMBER(18,0))),0) AS outhospital_time, " +
				"status,category,inpatient_ward, doctor_id, inpatient_info_id ,nurse_id ,birthday " +
				"FROM VIEW_ZY_DETAIL_PATIENT " +
				"WHERE " + w
		case 1000000000356:
			query = "SELECT " +
				"name, coalesce(id_card,''), mobile,sex, case when age=0 then EXTRACT(YEAR FROM SYSDATE) - EXTRACT(YEAR FROM birthday) ELSE age END AS age, hospitalization_no, sickbed_no, " +
				"(CAST((inhospital_time - TO_DATE('1970-01-01 08:00:00', 'YYYY-MM-DD HH24:MI:SS')) * 24 * 60 * 60 AS NUMBER(18,0))) AS inhospital_time, " +
				"coalesce((CAST((outhospital_time - TO_DATE('1970-01-01 08:00:00', 'YYYY-MM-DD HH24:MI:SS')) * 24 * 60 * 60 AS NUMBER(18,0))),0) AS outhospital_time, " +
				"case when status is null then case when outhospital_time IS NULL THEN 1 ELSE 2 END ELSE status END AS status,catetegory,case when inpatient_ward = 106 then '女病区' when inpatient_ward=181 then '男病区' else inpatient_ward end as inpatient_ward, doctor_id, inpatient_info_id ,nurse_id ,birthday " +
				"FROM view_spt_patient " +
				"WHERE " + w
		default:
			query = "SELECT " +
				"name, coalesce(id_card,''), mobile, sex, " +
				"COALESCE(age, 0) AS age, " +
				"hospitalization_no, sickbed_no, " +
				"(CAST((inhospital_time - TO_DATE('1970-01-01 08:00:00', 'YYYY-MM-DD HH24:MI:SS')) * 24 * 60 * 60 AS NUMBER(18,0))) AS inhospital_time, " +
				"COALESCE((CAST((outhospital_time - TO_DATE('1970-01-01 08:00:00', 'YYYY-MM-DD HH24:MI:SS')) * 24 * 60 * 60 AS NUMBER(18,0))), 0) AS outhospital_time, " +
				"CASE WHEN status IS NULL THEN CASE WHEN outhospital_time IS NULL THEN 1 ELSE 2 END ELSE status END AS status, " +
				"catetegory, inpatient_ward, doctor_id, inpatient_info_id, nurse_id, birthday " +
				"FROM view_spt_patient " +
				"WHERE " + w
		}
		log.Printf(query)
		rows, err := db.Query(query)
		if err != nil {
			log.Println(err)
			return err
		}
		defer rows.Close()
		var totalRecords int64
		for rows.Next() {
			var patient Patients
			var (
				name               string
				id_card            string
				mobile             string
				sex                string
				age                int
				hospitalization_no string
				sickbed_no         string
				doctor_id          string
				inhospital_time    int64
				outhospital_time   int64
				status1            int
				catetegory         string // 分类
				inpatient_ward     string // 病区
				inpatient_info_id  string // 每次出入院一个ID
				nurse_id           string
				birthday           string
			)
			err = rows.Scan(&name, &id_card, &mobile, &sex, &age, &hospitalization_no, &sickbed_no, &inhospital_time, &outhospital_time, &status1, &catetegory, &inpatient_ward, &doctor_id, &inpatient_info_id, &nurse_id, &birthday)
			if err != nil {
				//log.Print(name)
				log.Printf("query error1", err)
				return err
			}
			patient.Name = strings.TrimSpace(name)
			patient.IDCard = strings.TrimSpace(id_card)
			patient.Mobile = strings.TrimSpace(mobile)
			patient.Sex = strings.TrimSpace(sex)
			patient.Age = age
			patient.HospitalizationNo = strings.TrimSpace(hospitalization_no)
			patient.SickbedNo = strings.TrimSpace(sickbed_no)
			patient.Category = strings.TrimSpace(catetegory)
			patient.InpatientWard = strings.TrimSpace(inpatient_ward)
			patient.DoctorID = strings.TrimSpace(doctor_id)
			patient.InhospitalTime = inhospital_time
			patient.OuthospitalTime = outhospital_time
			patient.InpatientInfoId = strings.TrimSpace(inpatient_info_id)
			patient.Status = status1
			patient.NurseId = nurse_id
			patient.Birthday = birthday
			*res = append(*res, patient)
			totalRecords++
		}
		log.Println(totalRecords)
		if err := rows.Err(); err != nil {
			log.Println(err)
			return err
		}
		return nil

	//处理mysql
	case "mysql":
		db := models.MysqlDB()
		query = "SELECT " +
			"name, " +
			"COALESCE(id_card, '') AS id_card, " +
			"COALESCE(mobile, '') AS mobile, " +
			"sex, " +
			"COALESCE(age, 0) AS age, " +
			"hospitalization_no, " +
			"sickbed_no, " +
			"UNIX_TIMESTAMP(inhospital_time) - UNIX_TIMESTAMP('1970-01-01 08:00:00') AS inhospital_time, " +
			"CASE " +
			"WHEN outhospital_time IS NULL THEN 0 " +
			"ELSE UNIX_TIMESTAMP(outhospital_time) - UNIX_TIMESTAMP('1970-01-01 08:00:00') " +
			"END AS outhospital_time, " +
			"CASE WHEN status IS NULL THEN CASE WHEN outhospital_time IS NULL THEN 1 ELSE 2 END ELSE status END AS status, " +
			"catetegory, inpatient_ward, doctor_id, inpatient_info_id, nurse_id, birthday " +
			"FROM view_spt_patient " +
			"WHERE " + w
		log.Printf(query)
		rows, err := db.Query(query)
		if err != nil {
			log.Println(err)
			return err
		}
		defer rows.Close()
		for rows.Next() {
			var patient Patients
			var name, id_card, mobile, sex, hospitalization_no, sickbed_no, catetegory, inpatient_ward, doctor_id, inpatient_info_id, nurse_id, birthday string
			var age, inhospital_time, outhospital_time, status1 int64
			err = rows.Scan(&name, &id_card, &mobile, &sex, &age, &hospitalization_no, &sickbed_no, &inhospital_time, &outhospital_time, &status1, &catetegory, &inpatient_ward, &doctor_id, &inpatient_info_id, &nurse_id, &birthday)
			if err != nil {
				log.Printf("MySQL query error: %v", err)
				return err
			}
			patient.Name = strings.TrimSpace(name)
			patient.IDCard = strings.TrimSpace(id_card)
			patient.Mobile = strings.TrimSpace(mobile)
			patient.Sex = strings.TrimSpace(sex)
			patient.Age = int(age)
			patient.HospitalizationNo = strings.TrimSpace(hospitalization_no)
			patient.SickbedNo = strings.TrimSpace(sickbed_no)
			patient.Category = strings.TrimSpace(catetegory)
			patient.InpatientWard = strings.TrimSpace(inpatient_ward)
			patient.DoctorID = strings.TrimSpace(doctor_id)
			patient.InhospitalTime = inhospital_time
			patient.OuthospitalTime = outhospital_time
			patient.InpatientInfoId = strings.TrimSpace(inpatient_info_id)
			patient.Status = int(status1)
			patient.NurseId = nurse_id
			patient.Birthday = birthday
			*res = append(*res, patient)
			totalRecords++
		}
		log.Println(totalRecords)
		if err := rows.Err(); err != nil {
			log.Println(err)
			return err
		}
		return nil

	}
	log.Println("未发现数据库")
	return nil
}

// GetLastTimeByRedis 从Redis获取最后同步时间
// 用于增量同步，获取上次同步的时间戳，避免重复同步数据
// 参数：
//   - tlClientId: 听力客户端ID
//   - status: 患者状态（In:入院, Out:出院, Up:在院）
//
// 返回：最后同步时间的Unix时间戳
func GetLastTimeByRedis(tlClientId int, status string) (int, error) {
	rs := cache.R()
	var lastTime int

	switch status {
	case "In": // 入院患者最后同步时间
		in, err := rs.Get(context.Background(), "InLastTime-"+strconv.Itoa(tlClientId)).Result()
		if err != nil {
			if err.Error() == "redis: nil" {
				// 键不存在，返回默认时间戳（24小时前）
				defaultTime := time.Now().AddDate(0, 0, -1).Unix()
				log.Printf("InLastTime键不存在，使用默认时间戳: %d", defaultTime)
				return int(defaultTime), nil
			}
			return lastTime, err
		}
		lastTime, err = strconv.Atoi(in)
		if err != nil {
			return lastTime, err
		}
		// 获取后删除Redis中的时间戳，避免重复使用
		rs.Del(context.Background(), "InLastTime-"+strconv.Itoa(tlClientId))
		return lastTime, nil

	case "Out": // 出院患者最后同步时间
		out, err := rs.Get(context.Background(), "OutLastTime-"+strconv.Itoa(tlClientId)).Result()
		if err != nil {
			if err.Error() == "redis: nil" {
				// 键不存在，返回默认时间戳（7天前）
				defaultTime := time.Now().AddDate(0, 0, -7).Unix()
				log.Printf("OutLastTime键不存在，使用默认时间戳: %d", defaultTime)
				return int(defaultTime), nil
			}
			return lastTime, err
		}
		lastTime, err = strconv.Atoi(out)
		if err != nil {
			return lastTime, err
		}
		// 获取后删除Redis中的时间戳，避免重复使用
		rs.Del(context.Background(), "OutLastTime-"+strconv.Itoa(tlClientId))
		return lastTime, nil

	case "Up": // 在院患者不需要时间戳（全量同步）
		return 0, nil

	default:
		return lastTime, fmt.Errorf("status ERR")
	}
}

// SavePatientByRedis 将患者数据保存到Redis
// 根据患者状态将数据分类保存到不同的Redis哈希表中
// 参数：
//   - data: 患者数据切片指针
//   - status: 患者状态（In:入院, Out:出院, Up:在院）
//   - tid: 听力客户端ID字符串
func SavePatientByRedis(data *[]Patients, status string, tid string) {
	rs := cache.R()
	RedisMap1 := make(map[string]string) // 存储在院患者数据（状态=1）
	RedisMap2 := make(map[string]string) // 存储出院患者数据（状态=2）

	// 遍历患者数据，按状态分类
	for _, patients := range *data {
		// 使用住院号+入院时间作为Redis键，确保唯一性
		key := patients.HospitalizationNo + strconv.FormatInt(patients.InhospitalTime, 10)

		// 将患者结构体转换为JSON字符串
		binaryData, err := structToJSON(patients)
		if err != nil {
			fmt.Println("structToJSON转换错误:", err)
			continue
		}

		// 根据患者状态分类存储
		if patients.Status == 1 { // 1=在院，2=出院
			RedisMap1[key] = binaryData
		} else {
			RedisMap2[key] = binaryData
		}
	}

	var err error
	// 根据同步状态将数据写入不同的Redis哈希表
	switch status {
	case "In": // 入院患者数据
		if len(RedisMap1) == 0 {
			return
		}
		err = rs.HMSet(context.Background(), "InPatient-"+tid, RedisMap1).Err()
		std.LogInfoAndFields(logrus.Fields{
			"topic":      "writeRedis",
			"tlClientId": tid,
			"status":     status,
		}, len(RedisMap1))

	case "Out": // 出院患者数据
		if len(RedisMap2) == 0 {
			return
		}
		err = rs.HMSet(context.Background(), "OutPatient-"+tid, RedisMap2).Err()
		std.LogInfoAndFields(logrus.Fields{
			"topic":      "writeRedis",
			"tlClientId": tid,
			"status":     status,
		}, len(RedisMap2))

	case "Up": // 在院患者数据（全量同步）
		if len(RedisMap1) == 0 {
			return
		}
		// 先删除旧数据，再写入新数据
		err = rs.Del(context.Background(), "UpData-"+tid).Err()
		err = rs.HMSet(context.Background(), "UpData-"+tid, RedisMap1).Err()
	}

	// 记录Redis操作错误
	if err != nil {
		std.LogErrorLnAndFields(logrus.Fields{
			"topic":      "writeRedis",
			"tlClientId": tid,
		}, status, "SavePatientByRedis:", err)
	}
}

// 结构体转换为JSON字符串
func structToJSON(data interface{}) (string, error) {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return "", err
	}
	return string(jsonData), nil
}

// 处理转码cp850->utf8
func cy(string2 string) string {
	decoder := simplifiedchinese.GB18030.NewDecoder()
	NameUtf8Bytes, err := decoder.Bytes([]byte(string2))
	if err != nil {
		fmt.Println("解码出错:", err)
		return "err"
	}
	return string(NameUtf8Bytes)
}

// RedisToDatabase 从Redis读取数据并写入本地数据库
func RedisToDatabase(tlClientId string, status string, dbName string) {
	// 检查TODB_SWITCH开关
	if models.Hospital.C.ToDbSwitch != "true" {
		log.Println("TODB_SWITCH is disabled, skipping database write")
		return
	}

	log.Println("Starting Redis to Database sync for status:", status)
	rs := cache.R()

	var redisKey string
	switch status {
	case "In":
		redisKey = "InPatient-" + tlClientId
	case "Out":
		redisKey = "OutPatient-" + tlClientId
	case "Up":
		redisKey = "UpData-" + tlClientId
	default:
		log.Println("Unknown status:", status)
		return
	}

	// 从Redis获取所有数据
	patientData, err := rs.HGetAll(context.Background(), redisKey).Result()
	if err != nil {
		std.LogErrorLnAndFields(logrus.Fields{
			"topic":      "redisToDb",
			"tlClientId": tlClientId,
			"status":     status,
		}, "Failed to read from Redis", err)
		return
	}

	if len(patientData) == 0 {
		log.Println("No data found in Redis for key:", redisKey)
		return
	}

	// 写入数据库
	var successCount, errorCount int
	for key, jsonData := range patientData {
		var patient Patients
		err := json.Unmarshal([]byte(jsonData), &patient)
		if err != nil {
			log.Printf("Failed to unmarshal patient data for key %s: %v", key, err)
			errorCount++
			continue
		}

		err = insertOrUpdatePatient(&patient, dbName)
		if err != nil {
			log.Printf("Failed to insert/update patient %s: %v", patient.HospitalizationNo, err)
			errorCount++
		} else {
			successCount++
		}
	}

	std.LogInfoAndFields(logrus.Fields{
		"topic":        "redisToDb",
		"tlClientId":   tlClientId,
		"status":       status,
		"successCount": successCount,
		"errorCount":   errorCount,
	}, "Redis to Database sync completed")
}

// insertOrUpdatePatient 插入或更新患者数据到数据库
func insertOrUpdatePatient(patient *Patients, dbName string) error {
	switch dbName {
	case "mysql":
		db := models.MysqlDB()

		// 先检查记录是否存在
		var count int
		checkQuery := "SELECT COUNT(*) FROM spt_patient WHERE hospitalization_no = ? AND inpatient_info_id = ?"
		err := db.QueryRow(checkQuery, patient.HospitalizationNo, patient.InpatientInfoId).Scan(&count)
		if err != nil {
			return fmt.Errorf("failed to check existing record: %v", err)
		}

		var query string
		var args []interface{}

		if count > 0 {
			// 更新现有记录
			query = `
				UPDATE spt_patient SET
					name = ?, id_card = ?, mobile = ?, sex = ?, age = ?, sickbed_no = ?,
					inhospital_time = FROM_UNIXTIME(? + UNIX_TIMESTAMP('1970-01-01 08:00:00')),
					outhospital_time = CASE WHEN ? = 0 THEN NULL ELSE FROM_UNIXTIME(? + UNIX_TIMESTAMP('1970-01-01 08:00:00')) END,
					status = ?, catetegory = ?, inpatient_ward = ?, doctor_id = ?, nurse_id = ?,
					birthday = ?, updated_at = CURRENT_TIMESTAMP
				WHERE hospitalization_no = ? AND inpatient_info_id = ?
			`
			args = []interface{}{
				patient.Name, patient.IDCard, patient.Mobile, patient.Sex, patient.Age, patient.SickbedNo,
				patient.InhospitalTime, patient.OuthospitalTime, patient.OuthospitalTime,
				patient.Status, patient.Category, patient.InpatientWard, patient.DoctorID, patient.NurseId,
				patient.Birthday, patient.HospitalizationNo, patient.InpatientInfoId,
			}
		} else {
			// 插入新记录
			query = `
				INSERT INTO spt_patient (
					name, id_card, mobile, sex, age, hospitalization_no, sickbed_no,
					inhospital_time, outhospital_time, status, catetegory,
					inpatient_ward, doctor_id, inpatient_info_id, nurse_id, birthday
				) VALUES (?, ?, ?, ?, ?, ?, ?,
					FROM_UNIXTIME(? + UNIX_TIMESTAMP('1970-01-01 08:00:00')),
					CASE WHEN ? = 0 THEN NULL ELSE FROM_UNIXTIME(? + UNIX_TIMESTAMP('1970-01-01 08:00:00')) END,
					?, ?, ?, ?, ?, ?, ?)
			`
			args = []interface{}{
				patient.Name, patient.IDCard, patient.Mobile, patient.Sex, patient.Age,
				patient.HospitalizationNo, patient.SickbedNo, patient.InhospitalTime,
				patient.OuthospitalTime, patient.OuthospitalTime, patient.Status,
				patient.Category, patient.InpatientWard, patient.DoctorID,
				patient.InpatientInfoId, patient.NurseId, patient.Birthday,
			}
		}

		_, err = db.Exec(query, args...)
		return err

	case "sqlserver":
		// TODO: 实现SQL Server的插入逻辑
		return fmt.Errorf("SQL Server insert not implemented yet")

	case "oracle":
		// TODO: 实现Oracle的插入逻辑
		return fmt.Errorf("Oracle insert not implemented yet")

	default:
		return fmt.Errorf("unsupported database type: %s", dbName)
	}
}
