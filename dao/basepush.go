// Package dao 数据访问层
// 负责医生和患者数据的获取、处理和推送
package dao

import (
	"fmt"
	"log"
	"os"
	"strconv"
	"strings"
	"time"

	"gitlab.itingluo.com/backend/ivankabasepush/models" // 数据模型
	std "gitlab.itingluo.com/backend/ivankastd"         // 标准库
	"gitlab.itingluo.com/backend/ivankastd/toolkit"     // 工具包
)

// AF_Doctor_1 医生信息结构体
// 用于从数据库查询医生基本信息
type AF_Doctor_1 struct {
	UserName string `gorm:"type:varchar(50);column:user_name"` // 医生用户名/编号
	DeptId   int64  `gorm:"type:bigint(50);column:dept_id"`    // 科室ID
	Name     string `gorm:"type:varchar(50);column:name"`      // 医生姓名
	RoleId   int64  `gorm:"type:bigint(50);column:role_id"`    // 角色ID
}

// GetAfDoctorList 获取医生信息并推送到API
// 从不同类型的数据库中获取医生信息，每12小时同步一次
// 参数：
//   - tl_client_id: 听力客户端ID
//   - Dbname: 数据库类型（sqlserver/oracle/sybase/mysql）
//   - TlClientName: 听力客户端名称
func GetAfDoctorList(tl_client_id string, Dbname string, TlClientName string) error {
	// 根据环境变量确定API主机地址
	var host string
	if os.Getenv("CONFIGOR_ENV") == "ivktest" {
		host = "https://api-sit.itingluo.com" // 测试环境
	} else {
		host = "https://api.itingluo.com" // 生产环境
	}
	log.Print("API Host:", host)

	var doctorList []*AfDoctorItem // 用于API推送的医生列表
	var results []*AF_Doctor_1     // 数据库查询结果

	// 根据数据库类型执行不同的查询逻辑
	switch Dbname {
	case "sqlserver":
		log.Println("连接到 SQL Server 数据库")
		db := models.DB()

		// 特定客户端使用不同的查询逻辑
		if tl_client_id == "1000000000385" {
			// 查询包含角色和科室信息的医生数据
			err := db.Table("His.view_spt_user").Select(" role_id, dept_id, user_name, name").Where("user_name in (SELECT doctor_id from view_spt_patient GROUP BY doctor_id )").Find(&results).Error
			if err != nil {
				log.Printf("SQL Server 查询错误: %v", err)
				return err
			}
		} else {
			// 查询基本医生信息
			err := db.Table("view_spt_user").Select("user_name, name").Where("user_name in (SELECT doctor_id from view_spt_patient GROUP BY doctor_id )").Find(&results).Error
			if err != nil {
				log.Printf("SQL Server 查询错误: %v", err)
				return err
			}
		}

	case "sybase":
		log.Println("连接到 Sybase 数据库")
		db := models.SybaseDB()

		// Sybase查询，使用VARBINARY处理字符编码问题
		rows, err := db.Query("select coalesce(role_id,0), coalesce(dept_id,0), cast(user_name as VARBINARY) as user_name, cast(name as VARBINARY) as name from view_spt_user")
		if err != nil {
			log.Printf("Sybase查询错误: %v", err)
			return err
		}
		defer rows.Close()

		// 遍历查询结果
		for rows.Next() {
			var afDoctor AF_Doctor_1
			var userName, name string
			var deptID, roleID int64

			err := rows.Scan(&roleID, &deptID, &userName, &name)
			if err != nil {
				log.Printf("Sybase扫描错误: %v", err)
				continue
			}

			// 处理Sybase字符编码转换
			afDoctor.Name = cy(strings.TrimSpace(name))
			afDoctor.UserName = cy(strings.TrimSpace(userName))
			afDoctor.DeptId = deptID
			afDoctor.RoleId = roleID
			results = append(results, &afDoctor)
		}
	case "mysql":
		log.Println("连接到 MySQL 数据库")
		db := models.MysqlDB()

		// MySQL查询医生信息
		rows, err := db.Query("SELECT COALESCE(user_name,''), COALESCE(name,'') FROM view_spt_user WHERE user_name IN (SELECT doctor_id FROM view_spt_patient GROUP BY doctor_id)")
		if err != nil {
			log.Printf("MySQL 查询错误: %v", err)
			return err
		}
		defer rows.Close()

		// 遍历MySQL查询结果
		for rows.Next() {
			var afDoctor AF_Doctor_1
			var userName, name string
			err := rows.Scan(&userName, &name)
			if err != nil {
				log.Printf("MySQL 扫描错误: %v", err)
				continue
			}
			afDoctor.Name = strings.TrimSpace(name)
			afDoctor.UserName = strings.TrimSpace(userName)
			results = append(results, &afDoctor)
		}

	case "oracle":
		log.Println("连接到 Oracle 数据库")
		db := models.OraclDB()

		// Oracle查询医生信息
		rows, err := db.Query("select  coalesce(user_name,''), coalesce(name,'') from view_spt_user t where user_name in (SELECT doctor_id from view_spt_patient GROUP BY doctor_id)")
		if err != nil {
			log.Printf("Oracle查询错误: %v", err)
			return err
		}
		defer rows.Close()

		// 遍历Oracle查询结果
		for rows.Next() {
			var afDoctor AF_Doctor_1
			var userName, name string
			err := rows.Scan(&userName, &name)
			if err != nil {
				log.Printf("Oracle扫描错误: %v", err)
				continue
			}
			afDoctor.Name = strings.TrimSpace(name)
			afDoctor.UserName = strings.TrimSpace(userName)
			results = append(results, &afDoctor)
		}
	}

	// 将数据库查询结果转换为API推送格式
	for _, v := range results {
		doctorList = append(doctorList, &AfDoctorItem{
			DoctorName: v.Name,     // 医生姓名
			DoctorNo:   v.UserName, // 医生编号
		})
		// 调试信息（已注释）
		//log.Println("医生姓名:", v.Name)
		//log.Println("医生编号:", v.UserName)
	}

	log.Println("医生列表长度:", len(doctorList))

	// 构建API推送URL
	url := fmt.Sprintf("%s/apiv1/psyhis/doctorlsit/af_save?tl_client_id=%s&tl_client_name=%s", host, tl_client_id, TlClientName)

	// 如果有医生数据，则推送到API
	if len(doctorList) > 0 {
		respInfo, err := toolkit.DoRequestPost(&SendAfDoctorListReq{
			Count:  int64(len(doctorList)), // 医生数量
			Doctor: doctorList,             // 医生列表
		}, url)
		if err != nil {
			return fmt.Errorf("POST请求错误: %v", err)
		}
		log.Println("GetAfDoctorList 响应:", string(respInfo), url)
	}
	return nil
}

// SendAfDoctorListReq 发送医生列表请求结构体
// 用于向API推送医生信息的请求格式
type SendAfDoctorListReq struct {
	Count  int64           `json:"count"`       // 医生数量
	Doctor []*AfDoctorItem `json:"doctor_list"` // 医生列表
}

// AfDoctorItem 医生信息项
// API推送时使用的医生信息格式
type AfDoctorItem struct {
	DoctorName string `json:"doctor_name"` // 医生姓名
	DoctorNo   string `json:"doctor_no"`   // 医生编号
}

// TickInPatientByRedis 定时执行患者数据同步到Redis
// 使用定时器定期调用患者数据同步功能
// 参数：
//   - d: 执行间隔时间
//   - Status: 患者状态（In/Out/Up）
//   - DbName: 数据库类型
func TickInPatientByRedis(d time.Duration, Status string, DbName string) {
	defer func() {
		if err := recover(); err != nil {
			std.LogRecover(err) // 记录panic错误
		}
	}()

	tickChan := time.NewTicker(d).C                          // 创建定时器
	tlClientId := strconv.Itoa(models.Hospital.C.TlClientId) // 获取客户端ID

	for {
		select {
		case <-tickChan:
			InPatientToRedis(tlClientId, Status, DbName) // 执行患者数据同步
		}
	}
}

// TickDoctorByRedis 定时执行医生数据同步
// 使用定时器定期调用医生数据获取和推送功能
// 参数：
//   - d: 执行间隔时间
//   - Status: 状态标识（通常为"Doctor"）
//   - DbName: 数据库类型
//   - TlClientName: 听力客户端名称
func TickDoctorByRedis(d time.Duration, Status string, DbName string, TlClientName string) {
	defer func() {
		if err := recover(); err != nil {
			std.LogRecover(err) // 记录panic错误
		}
	}()

	tickChan := time.NewTicker(d).C                          // 创建定时器
	tlClientId := strconv.Itoa(models.Hospital.C.TlClientId) // 获取客户端ID

	for {
		select {
		case <-tickChan:
			GetAfDoctorList(tlClientId, DbName, TlClientName) // 执行医生数据获取和推送
		}
	}
}
