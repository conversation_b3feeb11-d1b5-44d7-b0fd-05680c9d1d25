// Package common 公共配置管理包
// 负责应用程序的配置文件加载、解析和全局配置管理
package common

import (
	"log"

	_ "github.com/go-sql-driver/mysql"          // MySQL驱动
	std "gitlab.itingluo.com/backend/ivankastd" // 标准库
)

var (
	GlobalConf *Config // 全局配置实例
)

// IosPush iOS推送配置结构体
// 用于配置不同应用的iOS推送服务参数
type IosPush struct {
	IOSCertificateFileName string `yaml:"certificate_file_name"` // iOS证书文件名
	Password               string `yaml:"password"`              // 证书密码
	Development            bool   `yaml:"development"`           // 是否为开发环境
	QueueWorks             uint   `yaml:"queue_works"`           // 队列工作线程数
	ServiceCount           int    `yaml:"service_count"`         // 服务数量
}

// Config 应用程序主配置结构体
// 包含所有子系统的配置信息，从YAML配置文件中加载
type Config struct {
	// iOS推送服务配置 - 支持多个不同的应用推送配置
	GoldIosPushConf      IosPush `yaml:"gold_ios_push"`       // Gold应用iOS推送配置
	WeexIosPushConf      IosPush `yaml:"weex_ios_push"`       // Weex应用iOS推送配置
	WitsIosPushConf      IosPush `yaml:"wits_ios_push"`       // Wits应用iOS推送配置
	WitsIosEnterPushConf IosPush `yaml:"wits_ios_enter_push"` // Wits企业版iOS推送配置
	WSCNIosPushConf      IosPush `yaml:"wscn_ios_push"`       // WSCN应用iOS推送配置
	WSCNIosProPushConf   IosPush `yaml:"wscn_ios_pro_push"`   // WSCN专业版iOS推送配置
	XgbIosPushConf       IosPush `yaml:"xgb_ios_push"`        // XGB应用iOS推送配置
	CongIosPushConf      IosPush `yaml:"cong_ios_push"`       // Cong应用iOS推送配置
	CongIosEnterPushConf IosPush `yaml:"cong_ios_enter_push"` // Cong企业版iOS推送配置

	// 基础服务配置
	Log   std.ConfigLog     `json:"logger" yaml:"logger"` // 日志配置
	NSQ   std.ConfigNSQ     `yaml:"nsq"`                  // NSQ消息队列配置
	Redis std.ConfigRedis   `yaml:"redis"`                // Redis缓存配置
	Micro std.ConfigService `yaml:"micro"`                // 微服务配置

	// 数据库配置
	Mysql  std.ConfigMysql  `yaml:"mysql"`  // MySQL数据库配置
	Mssql  std.ConfigMssql  `yaml:"mssql"`  // SQL Server数据库配置
	Oracle std.ConfigOracle `yaml:"oracle"` // Oracle数据库配置

	// 其他服务配置
	Client        std.ConfigClient        `yaml:"client"`                             // 客户端配置
	ElasticSearch std.ConfigElasticSearch `json:"elasticSearch" yaml:"elasticSearch"` // ElasticSearch配置
}

// InitModel 初始化配置模型
// 从指定的配置文件中加载配置信息，并初始化日志系统
// 参数：fileName - 配置文件路径（支持YAML格式）
func InitModel(fileName string) {
	GlobalConf = new(Config)              // 创建配置实例
	std.LoadConf(GlobalConf, fileName)    // 从文件加载配置
	std.InitLog(GlobalConf.Log)           // 初始化日志系统
	log.Println("InitModel ", GlobalConf) // 输出配置加载完成信息
}
