// Package cache Redis缓存管理包
// 提供Redis连接初始化和常用操作接口，用于患者数据缓存、同步状态管理和分布式锁
package cache

import (
	"context"
	"time"

	redis "github.com/redis/go-redis/v9"            // Redis客户端库v9
	std "gitlab.itingluo.com/backend/ivankastd"     // 标准库
	"gitlab.itingluo.com/backend/ivankastd/toolkit" // 工具包
)

var (
	redisClient *redis.Client // Redis客户端实例
)

// InitRedis 初始化Redis连接
// 使用工具包中的InitRedisv9函数创建Redis客户端
// 参数：config - Redis配置信息
func InitRedis(config std.ConfigRedis) {
	redisClient = toolkit.InitRedisv9(config)
}

// R 获取Redis客户端实例
// 返回全局Redis客户端，供其他模块使用
func R() *redis.Client {
	return redisClient
}

// Acquire 获取分布式锁
// 使用Redis的SETNX命令实现分布式锁机制
// 参数：
//   - key: 锁的键名
//   - val: 锁的值
//   - expire: 锁的过期时间
//
// 返回：true表示获取锁成功，false表示锁已被占用
func Acquire(key, val string, expire time.Duration) bool {
	return redisClient.SetNX(context.Background(), key, val, expire).Val()
}

// Set 设置键值对
// 向Redis中设置一个带过期时间的键值对
// 参数：
//   - key: 键名
//   - value: 值
//   - timeOut: 过期时间（秒）
func Set(key, value string, timeOut int) {
	if redisClient == nil || key == "" || value == "" {
		return
	}
	redisClient.Set(context.Background(), key, value, time.Duration(timeOut)*time.Second)
}

// ZAdd 向有序集合添加成员
// 向Redis有序集合中添加一个成员及其分数
// 参数：
//   - key: 有序集合的键名
//   - score: 成员的分数
//   - member: 成员值
//
// 返回：添加的成员数量
func ZAdd(key string, score float64, member any) int64 {
	if redisClient == nil || key == "" || member == nil {
		return 0
	}
	return redisClient.ZAdd(context.Background(), key, redis.Z{Score: score, Member: member}).Val()
}

// ZRangeByScore 按分数范围获取有序集合成员
// 从Redis有序集合中按分数范围获取成员列表
// 参数：
//   - key: 有序集合的键名
//   - min: 最小分数
//   - max: 最大分数
//   - offset: 偏移量
//   - count: 返回数量限制
//
// 返回：符合条件的成员列表
func ZRangeByScore(key, min, max string, offset, count int64) []string {
	if redisClient == nil || key == "" || min == "" || max == "" {
		return nil
	}
	return redisClient.ZRangeByScore(context.Background(), key, &redis.ZRangeBy{Min: min, Max: max, Offset: offset, Count: count}).Val()
}

// ZRemRangeByScore 按分数范围删除有序集合成员
// 从Redis有序集合中删除指定分数范围内的成员
// 参数：
//   - key: 有序集合的键名
//   - min: 最小分数
//   - max: 最大分数
//
// 返回：删除的成员数量
func ZRemRangeByScore(key, min, max string) int64 {
	if redisClient == nil || key == "" || min == "" || max == "" {
		return 0
	}
	return redisClient.ZRemRangeByScore(context.Background(), key, min, max).Val()
}

// ZCard 获取有序集合成员数量
// 返回Redis有序集合中的成员总数
// 参数：key - 有序集合的键名
// 返回：成员数量
func ZCard(key string) int64 {
	if redisClient == nil || key == "" {
		return 0
	}
	return redisClient.ZCard(context.Background(), key).Val()
}

// Get 获取键的值
// 从Redis中获取指定键的字符串值
// 参数：key - 键名
// 返回：键对应的值，如果键不存在返回空字符串
func Get(key string) string {
	if redisClient == nil || key == "" {
		return ""
	}
	return redisClient.Get(context.Background(), key).Val()
}

// HScan 扫描哈希表
// 注意：此函数存在递归调用自身的问题，可能导致栈溢出
// 参数：
//   - key: 哈希表键名
//   - cursor: 游标位置
//   - match: 匹配模式
//   - count: 扫描数量
//
// 返回：扫描结果（当前实现有问题）
func HScan(key string, cursor uint64, match string, count int64) string {
	if redisClient == nil || key == "" {
		return ""
	}
	// TODO: 修复递归调用问题，应该调用redisClient.HScan
	return HScan(key, cursor, match, count)
}
