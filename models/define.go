// Package models 数据模型包
// 定义了医院信息系统的数据结构和数据库连接管理
package models

import (
	"database/sql"
	"fmt"

	std "gitlab.itingluo.com/backend/ivankastd"     // 标准库
	"gitlab.itingluo.com/backend/ivankastd/toolkit" // 工具包
	"gorm.io/gorm"                                  // GORM ORM框架
)

// HospitalInfo 医院信息结构体
// 包含医院的基本配置信息
type HospitalInfo struct {
	C *Config `json:"hospitalinfo"` // 医院配置信息
}

// Config 配置结构体
// 包含数据库连接、Redis连接和业务相关的配置信息
type Config struct {
	MssqlUsername string `json:"MSSQL_USERNAME"` // SQL Server用户名
	MssqlPassword string `json:"MSSQL_PASSWORD"` // SQL Server密码
	MssqlHost     string `json:"MSSQL_HOST"`     // SQL Server主机地址
	MssqlPort     string `json:"MSSQL_PORT"`     // SQL Server端口
	MssqlDBName   string `json:"MSSQL_DB_NAME"`  // SQL Server数据库名
	RedisHost     string `json:"REDIS_HOST"`     // Redis主机地址
	RedisPort     string `json:"REDIS_PORT"`     // Redis端口
	// RedisAuth        string `json:"REDIS_AUTH"`         // Redis认证密码
	RedisIdleTimeout string `json:"REDIS_IDLE_TIMEOUT"` // Redis空闲超时时间
	RedisDB          string `json:"REDIS_DB"`           // Redis数据库编号
	DbName           string `json:"DB_NAME"`            // 数据库类型名称(sqlserver/oracle/sybase/mysql)
	TlClientName     string `json:"TL_CLIENT_NAME"`     // 听力客户端名称
	TlClientId       int    `json:"TL_CLIENT_ID"`       // 听力客户端ID
	ToDbSwitch       string `json:"TODB_SWITCH"`        // 数据库同步开关(true/false)
}

// 全局数据库连接变量
var (
	db       *gorm.DB      // SQL Server数据库连接（使用GORM）
	oracleDb *sql.DB       // Oracle数据库连接
	sybaseDb *sql.DB       // Sybase数据库连接
	mysqlDb  *sql.DB       // MySQL数据库连接
	Hospital *HospitalInfo // 全局医院信息配置
)

// InitModel 初始化SQL Server数据库模型
// 使用GORM框架连接SQL Server数据库
func InitModel(config std.ConfigMssql) {
	db = toolkit.CreateMssqlDB(config)
}

// InitSyabseModel 初始化Sybase数据库模型
// 创建Sybase数据库连接
func InitSyabseModel(config std.ConfigMssql) {
	sybaseDb = CreateSybaseDB(config)
}

// SybaseDB 获取Sybase数据库连接
// 返回Sybase数据库连接实例
func SybaseDB() *sql.DB {
	return sybaseDb
}

// DB 获取SQL Server数据库连接
// 返回GORM数据库连接实例
func DB() *gorm.DB {
	return db
}

// Session 创建新的数据库会话
// 开始一个新的数据库事务
func Session() *gorm.DB {
	return db.Begin()
}

// CloseDB 关闭SQL Server数据库连接
// 注意：GORM v2中连接池会自动管理，通常不需要手动关闭
func CloseDB() {
	// GORM v2中连接池会自动管理，通常不需要手动关闭
	//if db != nil {
	//	db.Close()
	//}
}

// InitOracleModel 初始化Oracle数据库模型
// 创建Oracle数据库连接
func InitOracleModel(config std.ConfigMssql) {
	fmt.Println("Initializing Oracle database connection...")
	oracleDb = CreateOracleDB(config)
}

// OraclDB 获取Oracle数据库连接
// 返回Oracle数据库连接实例
func OraclDB() *sql.DB {
	return oracleDb
}

// CloseOracleDB 关闭Oracle数据库连接
// 安全地关闭Oracle数据库连接
func CloseOracleDB() {
	if oracleDb != nil {
		oracleDb.Close()
	}
}

// CloseSybaseDB 关闭Sybase数据库连接
// 安全地关闭Sybase数据库连接
func CloseSybaseDB() {
	if sybaseDb != nil {
		sybaseDb.Close()
	}
}

// InitMysqlModel 初始化MySQL数据库模型
// 创建MySQL数据库连接
func InitMysqlModel(config std.ConfigMssql) {
	mysqlDb = CreateMysqlDB(config)
}

// MysqlDB 获取MySQL数据库连接
// 返回MySQL数据库连接实例
func MysqlDB() *sql.DB {
	return mysqlDb
}

// CloseMysqlDB 关闭MySQL数据库连接
// 安全地关闭MySQL数据库连接
func CloseMysqlDB() {
	if mysqlDb != nil {
		mysqlDb.Close()
	}
}
