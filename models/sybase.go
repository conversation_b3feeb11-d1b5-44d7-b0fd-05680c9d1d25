// Package models 数据库连接实现包
// 提供Sybase、Oracle、MySQL数据库的连接创建功能
package models

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/go-sql-driver/mysql"                      // MySQL驱动
	_ "github.com/godror/godror"                            // Oracle驱动
	"github.com/thda/tds"                                   // Sybase TDS驱动
	_ "github.com/thda/tds"                                 // Sybase驱动注册
	std "gitlab.itingluo.com/backend/ivankastd"             // 标准库
	"gitlab.itingluo.com/backend/ivankastd/toolkit/strutil" // 字符串工具
	"golang.org/x/text/encoding/charmap"                    // 字符编码转换
)

// CreateSybaseDB 创建Sybase数据库连接
// 使用TDS协议连接Sybase数据库，支持CP850字符编码
func CreateSybaseDB(config std.ConfigMssql) *sql.DB {
	var (
		username = config.Username                       // 数据库用户名
		password = config.Password                       // 数据库密码
		host     = config.Host                           // 数据库主机地址
		port     = strutil.FromInt64(int64(config.Port)) // 数据库端口
		dbName   = config.DBName                         // 数据库名称
	)

	// 注册CP850字符编码，用于处理Sybase中的中文字符
	tds.RegisterEncoding("cp850", charmap.CodePage850)

	// 构建Sybase数据源连接字符串
	dataSourceName := fmt.Sprintf("tds://%s:%s@%s:%s/%s?language=us_english&charset=cp850",
		username, password, host, port, dbName)

	// 打开数据库连接
	db, err := sql.Open("tds", dataSourceName)
	if err != nil {
		log.Fatal(err)
	}

	// 测试数据库连接
	err = db.Ping()
	if err != nil {
		fmt.Println("Initial ping error:", err)
	}
	fmt.Println("Sybase database connection initialized")

	// 设置连接池参数
	db.SetMaxOpenConns(3) // 最大打开连接数
	db.SetMaxIdleConns(3) // 最大空闲连接数

	// 再次测试连接确保连接正常
	err = db.Ping()
	if err != nil {
		fmt.Println("Connection test failed:", err)
		panic(fmt.Sprintf("Failed to connect to Sybase %s:%s/%s: %s", host, port, dbName, err.Error()))
	}

	fmt.Println("Sybase database connection established successfully")
	return db
}

// CreateOracleDB 创建Oracle数据库连接
// 使用godror驱动连接Oracle数据库
func CreateOracleDB(config std.ConfigMssql) *sql.DB {
	var (
		username = config.Username                       // 数据库用户名
		password = config.Password                       // 数据库密码
		host     = config.Host                           // 数据库主机地址（格式：host:port/service_name）
		port     = strutil.FromInt64(int64(config.Port)) // 数据库端口（用于错误信息）
		dbName   = config.DBName                         // 数据库名称（用于错误信息）
	)

	// Oracle连接字符串示例: "**************:1521/orcl"
	// 构建Oracle数据源连接字符串
	dataSourceName := fmt.Sprintf("user=%s password=%s connectString=%s",
		username, password, host)
	fmt.Println("Oracle connection string:", dataSourceName)

	// 打开Oracle数据库连接
	db, err := sql.Open("godror", dataSourceName)
	fmt.Println("Oracle connection result:", db, err)
	if err != nil {
		fmt.Println("Oracle connection string error:", err)
		panic(fmt.Sprintf("Failed to connect to Oracle %s: %s", dataSourceName, err.Error()))
	}

	// 设置连接池参数
	db.SetMaxOpenConns(3) // 最大打开连接数
	db.SetMaxIdleConns(3) // 最大空闲连接数

	// 测试数据库连接
	err = db.Ping()
	if err != nil {
		fmt.Println("Oracle connection test failed:", err)
		panic(fmt.Sprintf("Failed to ping Oracle %s:%s/%s: %s", host, port, dbName, err.Error()))
	}

	fmt.Println("Oracle database connection established successfully")
	return db
}

// CreateMysqlDB 创建MySQL数据库连接
// 使用go-sql-driver/mysql驱动连接MySQL数据库
func CreateMysqlDB(config std.ConfigMssql) *sql.DB {
	var (
		username = config.Username                       // 数据库用户名
		password = config.Password                       // 数据库密码
		host     = config.Host                           // 数据库主机地址
		port     = strutil.FromInt64(int64(config.Port)) // 数据库端口
		dbName   = config.DBName                         // 数据库名称
	)

	// 构建MySQL数据源连接字符串
	// DSN格式: username:password@tcp(host:port)/dbname?params
	dataSourceName := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		username, password, host, port, dbName)

	// 打开MySQL数据库连接
	db, err := sql.Open("mysql", dataSourceName)
	if err != nil {
		log.Fatal(err)
	}

	// 测试数据库连接
	err = db.Ping()
	if err != nil {
		fmt.Println("MySQL connection error:", err)
		panic(fmt.Sprintf("Failed to connect to MySQL %s:%s/%s: %s", host, port, dbName, err.Error()))
	}

	fmt.Println("MySQL connection successful")

	// 设置连接池参数
	db.SetMaxOpenConns(10) // 最大打开连接数
	db.SetMaxIdleConns(5)  // 最大空闲连接数

	return db
}
