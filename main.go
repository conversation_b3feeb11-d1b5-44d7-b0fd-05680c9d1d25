// Package main 医院信息系统数据推送服务
// 该服务负责从医院的HIS系统（支持SQL Server、Oracle、Sybase、MySQL）中
// 获取患者和医生信息，并将数据推送到Redis缓存和远程API接口
package main

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"reflect"
	"strconv"
	"time"

	c "gitlab.itingluo.com/backend/ivankabasepush/cache" // Redis缓存操作
	"gitlab.itingluo.com/backend/ivankabasepush/common"  // 公共配置
	"gitlab.itingluo.com/backend/ivankabasepush/dao"     // 数据访问层
	"gitlab.itingluo.com/backend/ivankabasepush/models"  // 数据模型
	std "gitlab.itingluo.com/backend/ivankastd"          // 标准库
	"gitlab.itingluo.com/backend/ivankastd/toolkit"      // 工具包
)

// init 程序初始化函数
// 在main函数执行前自动调用，负责加载配置文件并初始化医院信息
func init() {
	s, err := StartProject()
	if err != nil {
		fmt.Println("[error]:", err)
		os.Exit(1)
		return
	}
	models.Hospital = s
}

// main 主函数
// 程序入口点，负责初始化各种服务组件并启动数据同步任务
func main() {
	// 初始化配置模型，加载YAML配置文件
	common.InitModel("./conf/ivankabasepush.ivtest.yaml")
	Config := common.GlobalConf
	var DbName string       // 数据库类型名称
	var TlClientName string // 听力客户端名称

	// 从医院配置中读取数据库连接信息并设置到全局配置
	Config.Mssql.Username = models.Hospital.C.MssqlUsername
	Config.Mssql.Password = models.Hospital.C.MssqlPassword
	Config.Mssql.Host = models.Hospital.C.MssqlHost
	Config.Mssql.Port, _ = strconv.Atoi(models.Hospital.C.MssqlPort)
	Config.Mssql.DBName = models.Hospital.C.MssqlDBName

	// 从医院配置中读取Redis连接信息并设置到全局配置
	Config.Redis.Host = models.Hospital.C.RedisHost
	Config.Redis.Port, _ = strconv.Atoi(models.Hospital.C.RedisPort)
	// Config.Redis.Auth = models.Hospital.C.RedisAuth
	Config.Redis.IdleTimeout, _ = strconv.Atoi(models.Hospital.C.RedisIdleTimeout)
	Config.Redis.DB, _ = strconv.Atoi(models.Hospital.C.RedisDB)

	// 设置业务相关配置
	DbName = models.Hospital.C.DbName
	TlClientName = models.Hospital.C.TlClientName
	tlid := strconv.Itoa(models.Hospital.C.TlClientId)

	// 打印配置信息用于调试
	log.Println("sql:", DbName)
	log.Println("sqlInfo:", Config.Mssql)
	log.Println("sqlRedis:", Config.Redis)
	log.Println("TlClientName:", TlClientName)
	log.Println("tlId:", tlid)
	log.Println("TODB_SWITCH:", models.Hospital.C.ToDbSwitch)

	// 初始化日志系统
	std.InitLog(Config.Log) // 初始化日志配置
	std.InitWriteToElasticSearch(Config.Log, Config.ElasticSearch, "ivankadedicated-api_robot", 0)

	// 初始化Redis连接
	c.InitRedis(Config.Redis)

	// 根据数据库类型初始化相应的数据库连接
	switch DbName {
	case "sqlserver":
		models.InitModel(Config.Mssql)
		defer models.CloseDB() // 程序结束时关闭数据库连接
		fmt.Println("start sqlserver")
	case "oracle":
		models.InitOracleModel(Config.Mssql)
		defer models.CloseOracleDB()
		fmt.Println("start oracle")
	case "sybase":
		models.InitSyabseModel(Config.Mssql)
		defer models.CloseSybaseDB()
		fmt.Println("start sybase")
	case "mysql":
		models.InitMysqlModel(Config.Mssql)
		defer models.CloseMysqlDB()
		fmt.Println("start mysql")
	}

	// 获取医生列表并推送到API
	dao.GetAfDoctorList(tlid, DbName, TlClientName)

	// 启动各种后台任务（使用goroutine并发执行）
	go Heartbeat()                              // 心跳检测任务
	go writeInPatientByRedis(DbName)            // 入院患者数据同步任务
	go writeOutPatientByRedis(DbName)           // 出院患者数据同步任务
	go writeUpPatientByRedis(DbName)            // 在院患者数据同步任务
	go writeDoctorByRedis(DbName, TlClientName) // 医生数据同步任务

	// 如果开启了数据库同步开关，启动Redis到数据库的同步任务
	if models.Hospital.C.ToDbSwitch == "true" {
		go redisToDbSync(DbName)
	}

	// 主线程保持运行，每3分钟检查一次
	for {
		time.Sleep(time.Minute * 3)
	}
}

// writeInPatientByRedis 入院患者数据同步到Redis
// 每1分钟执行一次，获取新入院的患者数据并写入Redis
func writeInPatientByRedis(DbName string) {
	dao.TickInPatientByRedis(time.Minute*1, "In", DbName)
}

// writeOutPatientByRedis 出院患者数据同步到Redis
// 每5分钟执行一次，获取新出院的患者数据并写入Redis
func writeOutPatientByRedis(DbName string) {
	dao.TickInPatientByRedis(time.Minute*5, "Out", DbName)
}

// writeUpPatientByRedis 在院患者数据同步到Redis
// 每2分钟执行一次，获取当前在院的患者数据并写入Redis
func writeUpPatientByRedis(DbName string) {
	dao.TickInPatientByRedis(time.Minute*2, "Up", DbName)
}

// writeDoctorByRedis 医生数据同步到Redis
// 每12小时执行一次，获取医生信息并推送到API
func writeDoctorByRedis(DbName string, TlClientName string) {
	dao.TickDoctorByRedis(time.Hour*12, "Doctor", DbName, TlClientName)
}

// redisToDbSync Redis数据同步到本地数据库
// 定期将Redis中的患者数据同步到本地数据库，用于数据备份和离线查询
func redisToDbSync(DbName string) {
	tlClientId := strconv.Itoa(models.Hospital.C.TlClientId)
	ticker := time.NewTicker(time.Minute * 5) // 每5分钟同步一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			log.Println("Starting Redis to Database sync...")

			// 同步入院患者数据
			dao.RedisToDatabase(tlClientId, "In", DbName)

			// 同步出院患者数据
			dao.RedisToDatabase(tlClientId, "Out", DbName)

			// 同步在院患者数据
			dao.RedisToDatabase(tlClientId, "Up", DbName)

			log.Println("Redis to Database sync completed")
		}
	}
}

// Heartbeat 心跳检测服务
// 定期向监控系统发送心跳信号，表明服务正常运行
func Heartbeat() {
	h := toolkit.NewHeartbeat("collectloop", models.Hospital.C.TlClientId, time.Minute*5, os.Getenv("VERSION"))
	h.Start()
}

// StartProject 启动项目并加载配置
// 从config.json文件中读取医院配置信息，并验证配置的完整性
func StartProject() (*models.HospitalInfo, error) {
	config := &models.HospitalInfo{}
	// 配置文件路径（支持本地开发和生产环境）
	//configPath := "/data/docker-compose/eli/conf/config.json"  // 生产环境路径
	configPath := "./conf/config.json" // 开发环境路径

	// 检查配置文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return config, fmt.Errorf("config file does not exist: %s", configPath)
	}

	// 读取配置文件内容
	fileContent, err := ioutil.ReadFile(configPath)
	if err != nil {
		return config, fmt.Errorf("error reading config file: %w", err)
	}

	// 解析JSON配置文件
	if err := json.Unmarshal(fileContent, config); err != nil {
		return config, fmt.Errorf("error unmarshaling config file: %w", err)
	}

	// 使用反射验证配置字段的完整性
	v := reflect.ValueOf(config.C).Elem()
	for i := 0; i < v.NumField(); i++ {
		field := v.Field(i)
		fieldName := v.Type().Field(i).Name

		// 跳过密码字段的检查（密码可以为空）
		if fieldName == "MssqlPassword" {
			continue
		}

		// 检查字符串字段是否为空
		if field.Kind() == reflect.String && field.String() == "" {
			return config, fmt.Errorf("config field %s is empty", fieldName)
		}

		// 检查整数字段是否为0
		if field.Kind() == reflect.Int && field.Int() == 0 {
			return config, fmt.Errorf("config field %s is empty", fieldName)
		}
	}

	return config, nil
}
